# Requisitos Funcionales - Arroyo University

## Introducción

Este documento contiene todas las Historias de Usuario (HU) y Criterios de Aceptación (CA) para Arroyo University. Cada requisito funcional está estructurado siguiendo metodologías ágiles con criterios verificables que incluyen métricas, estados HTTP y efectos en la base de datos.

---

## 1. Gestión de Tenants y Configuración

### HU-01: Alta y baja de tenants
**Como** SysAdmin  
**Quiero** crear y gestionar tenants con aprovisionamiento automático  
**Para** que nuevas organizaciones puedan comenzar a usar la plataforma inmediatamente

**Criterios de Aceptación:**
- **CA1**: URLs en formato `{tenant}.arroyo.app` responden `200 OK` < 1s tras creación
- **CA2**: La creación dispara pipeline Terraform y registra backup `tag="bootstrap"`
- **CA3**: La baja marca `Tenants.deleted_at` y elimina contenedor Blob
- **CA4**: Wizard de onboarding enviado por email al Admin Tenant

### HU-02: Configuraciones globales por tenant
**Como** Admin Tenant  
**Quiero** personalizar configuraciones de mi organización  
**Para** adaptar la plataforma a nuestras necesidades específicas

**Criterios de Aceptación:**
- **CA1**: `GET /config` devuelve merge global+tenant < 100ms
- **CA2**: `PATCH /config` solo Admin Tenant; genera `AuditLogs`
- **CA3**: Cambios reflejados en tiempo real sin reinicio
- **CA4**: Rollback disponible para últimos 10 cambios

### HU-03: Auto-bloqueo de cuota IA
**Como** Sistema  
**Quiero** controlar automáticamente el uso de servicios IA  
**Para** evitar sobrecostos y garantizar fair usage

**Criterios de Aceptación:**
- **CA1**: >90% cuota crea `SystemAlert` *WARNING*
- **CA2**: 100% → `/questions/generate` devuelve `429 Quota Exceeded`
- **CA3**: Notificación automática a Admin Tenant y billing contact
- **CA4**: Upgrade automático disponible vía Stripe

---

## 2. Gestión de Usuarios y Autenticación

### HU-04: CRUD usuarios y verificación email
**Como** Admin Tenant  
**Quiero** gestionar usuarios de mi organización  
**Para** controlar el acceso y mantener la seguridad

**Criterios de Aceptación:**
- **CA1**: `POST /users` envía email con token expira 24h
- **CA2**: Solo `Users.verified=TRUE` pueden iniciar sesión
- **CA3**: Invitaciones masivas vía CSV procesadas < 3min (Celery)
- **CA4**: Usuarios inactivos >90 días marcados para revisión

### HU-05: Autenticación multifactor (MFA)
**Como** Usuario  
**Quiero** habilitar MFA en mi cuenta  
**Para** aumentar la seguridad de mis datos

**Criterios de Aceptación:**
- **CA1**: Flujo TOTP (RFC 6238) + 5 códigos de recuperación
- **CA2**: Si `enforce_mfa`, redirige a setup obligatorio
- **CA3**: Backup codes descargables en PDF
- **CA4**: Logs de todos los intentos de autenticación

### HU-06: SSO empresarial
**Como** Admin Tenant  
**Quiero** integrar con nuestro proveedor de identidad  
**Para** simplificar el acceso de empleados

**Criterios de Aceptación:**
- **CA1**: Soporta SAML 2.0 & OIDC; metadata en `TenantSSOConfigs`
- **CA2**: Mapea groups IdP → `Roles` automáticamente
- **CA3**: Wizard de configuración con test de conexión < 30s
- **CA4**: Fallback a login local solo para SysAdmin

### HU-07: Usuarios temporales
**Como** Admin Tenant  
**Quiero** crear usuarios con acceso limitado por tiempo  
**Para** evaluaciones externas o candidatos

**Criterios de Aceptación:**
- **CA1**: Invitación con expiración configurable (1h - 30 días)
- **CA2**: Acceso solo a exámenes específicos asignados
- **CA3**: Auto-desactivación tras expiración
- **CA4**: Resultados exportables antes de expiración

---

## 3. Gestión de Roles y Permisos (RBAC)

### HU-08: Crear y editar roles
**Como** Admin Tenant  
**Quiero** crear roles personalizados con permisos específicos  
**Para** delegar responsabilidades de forma granular

**Criterios de Aceptación:**
- **CA1**: UI lista 100% permisos de `Permissions` categorizados
- **CA2**: `POST /roles` inicia `version=1` con auditoría
- **CA3**: Interfaz tipo Discord con categorías expandibles
- **CA4**: Preview de cambios antes de aplicar

### HU-09: Clonar y versionar roles
**Como** Admin Tenant  
**Quiero** clonar roles existentes y mantener versiones  
**Para** facilitar la gestión y tener trazabilidad

**Criterios de Aceptación:**
- **CA1**: `/roles/{id}/clone` crea copia con sufijo "(Copy)"
- **CA2**: Permisos clonados visibles inmediatamente
- **CA3**: Historial de versiones con diff visual
- **CA4**: Rollback a versión anterior disponible

### HU-10: Asignar roles a usuarios
**Como** Admin Tenant  
**Quiero** asignar y revocar roles de usuarios  
**Para** controlar el acceso según responsabilidades

**Criterios de Aceptación:**
- **CA1**: Cache RBAC refresca < 10s tras cambio
- **CA2**: Notificación push al destinatario
- **CA3**: Asignación masiva vía selección múltiple
- **CA4**: Validación de conflictos de permisos

### HU-11: Permisos temporales
**Como** Admin Tenant  
**Quiero** otorgar permisos por tiempo limitado  
**Para** acceso temporal sin cambios permanentes

**Criterios de Aceptación:**
- **CA1**: Valida `active_from` / `expires_at` ≥ 1min
- **CA2**: Cron revoca expirados cada hora
- **CA3**: Notificación 24h antes de expiración
- **CA4**: Extensión automática con aprobación

### HU-12: Auditoría de cambios RBAC
**Como** Admin Tenant  
**Quiero** ver historial de cambios de permisos  
**Para** cumplir con auditorías y compliance

**Criterios de Aceptación:**
- **CA1**: Diffs JSON en `AuditLogs` con before/after
- **CA2**: CSV exportable por SysAdmin
- **CA3**: Filtros por usuario, fecha, tipo de cambio
- **CA4**: Alertas automáticas para cambios críticos

---

## 4. Gestión de Cursos y Contenido

### HU-13: CRUD de cursos
**Como** Content Creator  
**Quiero** crear y gestionar cursos  
**Para** estructurar el contenido educativo

**Criterios de Aceptación:**
- **CA1**: `POST /courses` crea estado *DRAFT*
- **CA2**: Borrado lógico vía `is_archived`
- **CA3**: Versionado automático en cambios publicados
- **CA4**: Preview disponible antes de publicación

### HU-14: Contenidos multimedia
**Como** Content Creator  
**Quiero** subir y gestionar archivos multimedia  
**Para** enriquecer el contenido educativo

**Criterios de Aceptación:**
- **CA1**: ≤ 200MB, MIME válido o `415 Unsupported Media Type`
- **CA2**: Checksum `sha256` guardado para integridad
- **CA3**: Compresión automática de imágenes y videos
- **CA4**: CDN con URLs presignadas para acceso

### HU-15: Clonar cursos
**Como** Content Creator  
**Quiero** duplicar cursos existentes  
**Para** reutilizar estructura y contenido

**Criterios de Aceptación:**
- **CA1**: `/courses/{id}/clone` duplica y agrega "(Copy)"
- **CA2**: Mantiene `tenant_id` y resetea estado a DRAFT
- **CA3**: Referencias a archivos multimedia copiadas
- **CA4**: Opción de clonar solo estructura sin contenido

### HU-16: Prerrequisitos y fechas
**Como** Content Creator  
**Quiero** configurar prerrequisitos y fechas de cierre  
**Para** controlar el flujo de aprendizaje

**Criterios de Aceptación:**
- **CA1**: `closing_at` > `NOW()`; auto-close programado
- **CA2**: Prerrequisito faltante → `409 Conflict`
- **CA3**: Notificaciones automáticas antes del cierre
- **CA4**: Extensión de fechas con justificación

---

## 5. Banco de Preguntas e IA

### HU-17: Crear preguntas manuales
**Como** Content Creator  
**Quiero** crear preguntas manualmente  
**Para** tener control total sobre el contenido

**Criterios de Aceptación:**
- **CA1**: Soporte Markdown + preview en tiempo real
- **CA2**: Editar versión publicada crea nueva `version`
- **CA3**: Validación de estructura según tipo de pregunta
- **CA4**: Tags automáticos basados en contenido

### HU-18: Generar preguntas con IA
**Como** Content Creator  
**Quiero** generar preguntas automáticamente  
**Para** acelerar la creación de contenido

**Criterios de Aceptación:**
- **CA1**: Prompt incluye CEFR, skill, tono; latencia < 6s/5 preguntas
- **CA2**: `status=READY` + WebSocket notification
- **CA3**: Preview y edición antes de guardar
- **CA4**: Tracking de tokens IA consumidos

### HU-19: Importar preguntas
**Como** Content Creator  
**Quiero** importar preguntas desde archivos  
**Para** migrar contenido existente

**Criterios de Aceptación:**
- **CA1**: Soporta CSV y QTI; errores indican línea/columna
- **CA2**: Archivo original guardado para referencia
- **CA3**: Validación previa con reporte de errores
- **CA4**: Importación por lotes con progress bar

### HU-20: Versionado de preguntas
**Como** Content Creator  
**Quiero** mantener versiones de preguntas  
**Para** trazabilidad y rollback

**Criterios de Aceptación:**
- **CA1**: `is_archived` oculta en picker pero conserva FKs
- **CA2**: Solo última versión editable
- **CA3**: Comparación visual entre versiones
- **CA4**: Estadísticas de uso por versión

### HU-21: Moderación automática
**Como** Sistema  
**Quiero** detectar contenido inapropiado automáticamente  
**Para** mantener calidad y compliance

**Criterios de Aceptación:**
- **CA1**: Contenido ofensivo → `flagged=TRUE`
- **CA2**: Digest diario para Admin Tenant
- **CA3**: Escalación automática para contenido crítico
- **CA4**: Whitelist de términos técnicos por dominio

---

## 6. Exámenes y Evaluación

### HU-22: Crear y configurar exámenes
**Como** Content Creator  
**Quiero** crear exámenes con configuración flexible  
**Para** evaluar competencias específicas

**Criterios de Aceptación:**
- **CA1**: Estado *DRAFT*; máximo 1 placement test por curso
- **CA2**: `time_limit_sec` ≤ 10,000 (2.7 horas)
- **CA3**: Configuración de intentos permitidos
- **CA4**: Randomización de preguntas opcional

### HU-23: Seleccionar preguntas
**Como** Content Creator  
**Quiero** seleccionar preguntas para exámenes  
**Para** crear evaluaciones balanceadas

**Criterios de Aceptación:**
- **CA1**: Búsqueda full-text con pg_trgm
- **CA2**: Mínimo 1 pregunta por skill evaluado
- **CA3**: Distribución automática por dificultad
- **CA4**: Preview del examen completo

### HU-24: Temporizador de examen
**Como** Content Creator  
**Quiero** configurar límites de tiempo  
**Para** estandarizar las evaluaciones

**Criterios de Aceptación:**
- **CA1**: Cuenta atrás global o por pregunta
- **CA2**: Expirado → status *TIMEOUT* automático
- **CA3**: Advertencias a 10min, 5min, 1min
- **CA4**: Extensión de tiempo para accesibilidad

### HU-25: Preguntas condicionales
**Como** Content Creator  
**Quiero** configurar lógica de branching  
**Para** exámenes adaptativos (futuro)

**Criterios de Aceptación:**
- **CA1**: Columna `branch_logic JSONB` preparada
- **CA2**: UI oculta configuración hasta feature flag
- **CA3**: Validación de lógica circular
- **CA4**: Simulación de flujo antes de publicar

### HU-26: Exportar examen a PDF
**Como** Content Creator  
**Quiero** generar versión imprimible  
**Para** backup y distribución offline

**Criterios de Aceptación:**
- **CA1**: `/export?format=pdf` + firma digital
- **CA2**: PDF retenido 30 días en storage
- **CA3**: Watermark con información del tenant
- **CA4**: Opción de incluir/excluir respuestas correctas

---

## 7. Presentación de Exámenes

### HU-27: Iniciar y reanudar intento
**Como** Estudiante  
**Quiero** presentar exámenes con posibilidad de reanudar  
**Para** completar evaluaciones sin perder progreso

**Criterios de Aceptación:**
- **CA1**: Único intento `IN_PROGRESS` por usuario
- **CA2**: Desconexión > 30min → status *ABANDONED*
- **CA3**: Reanudación desde última pregunta guardada
- **CA4**: Tracking de tiempo real transcurrido

### HU-28: Autosave de respuestas
**Como** Estudiante  
**Quiero** que mis respuestas se guarden automáticamente  
**Para** no perder trabajo por problemas técnicos

**Criterios de Aceptación:**
- **CA1**: Patch cada 10s; pérdida máxima ≤ 20 caracteres
- **CA2**: UI muestra "guardado" con status `204`
- **CA3**: Indicador visual de estado de guardado
- **CA4**: Recuperación automática tras reconexión

### HU-29: Reproducción de audio
**Como** Estudiante  
**Quiero** escuchar audio de preguntas listening  
**Para** completar evaluaciones auditivas

**Criterios de Aceptación:**
- **CA1**: HLS adaptativo, fallback MP3
- **CA2**: Botón repetir 1 vez si permitido por configuración
- **CA3**: Controles de volumen y velocidad
- **CA4**: Subtítulos opcionales para accesibilidad

### HU-30: Grabación de audio
**Como** Estudiante  
**Quiero** grabar respuestas de speaking  
**Para** demostrar competencias orales

**Criterios de Aceptación:**
- **CA1**: WebRTC Opus 48kHz; máximo 90 segundos
- **CA2**: Preview con opción de re-grabación única
- **CA3**: Indicador visual de nivel de audio
- **CA4**: Compresión automática para optimizar storage

### HU-31: Funcionalidad offline (PWA)
**Como** Estudiante  
**Quiero** continuar examen sin conexión  
**Para** completar evaluaciones en cualquier lugar

**Criterios de Aceptación:**
- **CA1**: IndexedDB ≤ 50MB, LRU purge automático
- **CA2**: Sync automático al recuperar conexión
- **CA3**: Banner de estado offline/online
- **CA4**: Validación de integridad post-sync

### HU-32: Accesibilidad
**Como** Usuario con discapacidad  
**Quiero** navegar con tecnologías asistivas  
**Para** acceso equitativo a evaluaciones

**Criterios de Aceptación:**
- **CA1**: Atajos de teclado (N/P para navegación)
- **CA2**: Contraste WCAG AA (4.5:1 mínimo)
- **CA3**: Screen reader compatible
- **CA4**: Tiempo extendido configurable

---

## 8. Corrección y Scoring

### HU-33: Auto-scoring MCQ
**Como** Sistema
**Quiero** calificar automáticamente preguntas de opción múltiple
**Para** proporcionar resultados inmediatos

**Criterios de Aceptación:**
- **CA1**: Respuesta correcta = `ai_score=1.0`
- **CA2**: Sumatoria simple para score total
- **CA3**: Procesamiento < 100ms por pregunta
- **CA4**: Logging de todas las calificaciones

### HU-34: Scoring con IA
**Como** Sistema
**Quiero** calificar respuestas abiertas con IA
**Para** evaluación objetiva y escalable

**Criterios de Aceptación:**
- **CA1**: GPT-4 con rúbrica 0-5 puntos < 4s
- **CA2**: `rubric_json` con campos `coherence`, `grammar`, `pronunciation`
- **CA3**: Confidence score para validación manual
- **CA4**: Fallback a evaluación manual si confidence < 70%

### HU-35: Detección de plagio
**Como** Sistema
**Quiero** detectar similitud en respuestas
**Para** mantener integridad académica

**Criterios de Aceptación:**
- **CA1**: Integración Turnitin; < 15% pasa automáticamente
- **CA2**: > 40% → `flagged=TRUE` para revisión manual
- **CA3**: Comparación interna entre respuestas del mismo examen
- **CA4**: Reporte detallado de similitudes encontradas

### HU-36: Override manual
**Como** Instructor
**Quiero** corregir calificaciones automáticas
**Para** ajustar evaluaciones según criterio experto

**Criterios de Aceptación:**
- **CA1**: Evaluador edita `ai_score`; guarda `updated_by`
- **CA2**: Notificación instantánea al candidato
- **CA3**: Justificación obligatoria para cambios > 1 punto
- **CA4**: Auditoría completa de overrides

---

## 9. Analítica y Reportes

### HU-37: Dashboards personalizables
**Como** Admin Tenant
**Quiero** visualizar métricas clave
**Para** tomar decisiones basadas en datos

**Criterios de Aceptación:**
- **CA1**: Filtros por equipo, fechas, cursos
- **CA2**: Carga < 2s para datasets de 10k registros
- **CA3**: Widgets drag-and-drop personalizables
- **CA4**: Exportación de gráficos en PNG/PDF

### HU-38: Exportación de datos
**Como** Admin Tenant
**Quiero** exportar datos en formatos estándar
**Para** análisis externo y compliance

**Criterios de Aceptación:**
- **CA1**: UTF-8, separador configurable para CSV
- **CA2**: Enlaces de descarga expiran en 24h
- **CA3**: Formatos: CSV, JSON, Excel, PDF
- **CA4**: Filtros aplicables antes de exportación

### HU-39: Webhooks para integraciones
**Como** Admin Tenant
**Quiero** recibir notificaciones automáticas
**Para** integrar con sistemas externos

**Criterios de Aceptación:**
- **CA1**: JSON API 1.0 + HMAC signature
- **CA2**: 5 reintentos con backoff exponencial
- **CA3**: Logs detallados de entregas exitosas/fallidas
- **CA4**: Test endpoint para validación

### HU-40: Analítica predictiva
**Como** Admin Tenant
**Quiero** predicciones sobre rendimiento
**Para** intervención temprana y mejora continua

**Criterios de Aceptación:**
- **CA1**: Modelo recalibra semanalmente
- **CA2**: AUC ≥ 75% en predicciones
- **CA3**: Alertas automáticas para estudiantes en riesgo
- **CA4**: Recomendaciones de contenido personalizado

---

## 10. Notificaciones y Comunicación

### HU-41: Notificaciones de finalización
**Como** Estudiante
**Quiero** recibir notificación cuando mis resultados estén listos
**Para** conocer mi progreso inmediatamente

**Criterios de Aceptación:**
- **CA1**: Email + push notification ≤ 2min tras corrección
- **CA2**: Link directo a feedback detallado
- **CA3**: Resumen de puntuación en la notificación
- **CA4**: Opción de mute por tipo de notificación

### HU-42: Alertas de cuota IA
**Como** Admin Tenant
**Quiero** recibir alertas de uso de IA
**Para** gestionar costos y planificar upgrades

**Criterios de Aceptación:**
- **CA1**: Alertas a 80/90/100% de cuota (INFO/WARN/CRITICAL)
- **CA2**: Canales: Slack + email configurables
- **CA3**: Proyección de uso basada en tendencia
- **CA4**: Link directo a upgrade de plan

### HU-43: Recordatorios de expiración
**Como** Sistema
**Quiero** recordar sobre exámenes próximos a expirar
**Para** maximizar tasa de completación

**Criterios de Aceptación:**
- **CA1**: Recordatorios a 24h y 1h antes de expiración
- **CA2**: No duplicar si examen ya terminado
- **CA3**: Personalización de horarios de envío
- **CA4**: Tracking de efectividad de recordatorios

---

## 11. Administración y Operaciones

### HU-44: Backups automáticos
**Como** SysAdmin
**Quiero** backups automáticos y confiables
**Para** garantizar continuidad del negocio

**Criterios de Aceptación:**
- **CA1**: Backup nocturno con gzip + checksum; retención 30 días
- **CA2**: `Backups.status` SUCCESS/FAIL con alertas
- **CA3**: Verificación de integridad automática
- **CA4**: Backup incremental diario, completo semanal

### HU-45: Restauración selectiva
**Como** SysAdmin
**Quiero** restaurar datos específicos
**Para** recuperación granular sin downtime completo

**Criterios de Aceptación:**
- **CA1**: `dry_run` muestra impacto antes de ejecutar
- **CA2**: Solo SysAdmin autorizado; log completo en audit
- **CA3**: Restauración por tenant sin afectar otros
- **CA4**: Rollback automático si restauración falla

### HU-46: Gestión de suscripciones
**Como** Admin Tenant
**Quiero** gestionar mi plan de suscripción
**Para** controlar costos y capacidades

**Criterios de Aceptación:**
- **CA1**: Stripe prorratea automáticamente upgrades/downgrades
- **CA2**: Actualiza `Tenants.plan` y muestra banner < 10s
- **CA3**: Downgrade con uso actual alto → `SystemAlert` grace 24h
- **CA4**: Historial completo de cambios de plan

### HU-47: Facturación automática
**Como** Sistema
**Quiero** procesar pagos automáticamente
**Para** operación sin intervención manual

**Criterios de Aceptación:**
- **CA1**: `invoice.paid` webhook → `TenantInvoices.PAID` + PDF email
- **CA2**: Tres reintentos de pago fallido; luego grace period 7 días
- **CA3**: PDF de factura retenido 7 años para compliance
- **CA4**: Alertas automáticas para pagos fallidos

### HU-48: Gestión de impagos
**Como** Sistema
**Quiero** manejar cuentas morosas automáticamente
**Para** proteger recursos y incentivar pagos

**Criterios de Aceptación:**
- **CA1**: Bloquea generación IA + nuevas invitaciones; intentos activos continúan
- **CA2**: Desbloqueo automático inmediato tras pago exitoso
- **CA3**: Notificaciones escaladas: 3, 7, 14 días de mora
- **CA4**: Exportación de datos disponible antes de suspensión

---

## 12. Integraciones Externas

### HU-49: Configuración SSO
**Como** Admin Tenant
**Quiero** configurar Single Sign-On
**Para** simplificar acceso de empleados

**Criterios de Aceptación:**
- **CA1**: Wizard valida `metadata.xml` + test login < 30s
- **CA2**: IdP caído → fallback a login local solo para SysAdmin
- **CA3**: Mapeo automático de grupos a roles
- **CA4**: Sincronización de usuarios en tiempo real

### HU-50: Webhooks HRIS/LMS
**Como** Admin Tenant
**Quiero** integrar con sistemas de RRHH
**Para** sincronización automática de datos

**Criterios de Aceptación:**
- **CA1**: Firmas HMAC con secreto por tenant
- **CA2**: Retry automático y logs en `WebhookLogs`
- **CA3**: Rate limiting configurable por endpoint
- **CA4**: Transformación de datos configurable

### HU-51: API pública
**Como** Desarrollador externo
**Quiero** acceder a funcionalidades vía API
**Para** crear integraciones personalizadas

**Criterios de Aceptación:**
- **CA1**: OAuth2 con scopes específicos por funcionalidad
- **CA2**: Rate limiting 1000 RPM; `429` si excede
- **CA3**: Documentación OpenAPI completa con ejemplos
- **CA4**: SDK en Python, JavaScript, PHP

---

## Conclusión

Estos 51 requisitos funcionales proporcionan la base completa para el desarrollo de Arroyo University, cubriendo todos los aspectos desde la gestión multi-tenant hasta integraciones externas. Cada historia de usuario incluye criterios de aceptación verificables que aseguran la calidad, completitud y trazabilidad de la implementación.

La estructura modular permite desarrollo incremental mientras mantiene coherencia en la experiencia de usuario y cumplimiento de estándares internacionales de calidad y seguridad.
