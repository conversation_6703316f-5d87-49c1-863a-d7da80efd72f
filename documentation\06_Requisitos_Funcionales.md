# Requisitos Funcionales - Arroyo University

## Introducción

Este documento contiene todas las Historias de Usuario (HU) y Criterios de Aceptación (CA) para Arroyo University. Cada requisito funcional está estructurado siguiendo metodologías ágiles con criterios verificables que incluyen métricas, estados HTTP y efectos en la base de datos.

---

## 1. Gestión de Tenants y Configuración

### HU-01: Enable Creation and Deletion of Tenant Plans

**User Story**
As a platform administrator
I want to create and delete tenants and their associated plans
So that I can manage client lifecycles with automated infrastructure setup and cleanup

**Acceptance Criteria (Gherkin Format)**
**Given** a new tenant is being created
**When** the setup process is triggered
**Then** a URL in the format {tenant}.arroyo.app should return HTTP 200 OK in under 1 second

**Given** a new tenant is being created
**When** the creation completes
**Then** the Terraform pipeline should be triggered and the backup tagged "bootstrap"

**Given** a tenant is being deleted
**When** the deletion process is executed
**Then** the system should mark Tenants.deleted_at and remove the tenant's Blob container

**Given** a new tenant is created
**When** the onboarding process begins
**Then** a wizard onboarding email should be sent to the Admin Tenant

### HU-02: Configure Global Tenant Settings

**User Story**
As a tenant administrator
I want to customize my organization's platform configurations
So that I can adapt the platform to our specific business needs and branding

**Acceptance Criteria (Gherkin Format)**
**Given** I am an authenticated Admin Tenant
**When** I request the configuration via GET /config
**Then** the system should return merged global and tenant-specific settings in under 100ms

**Given** I am an authenticated Admin Tenant
**When** I update configuration via PATCH /config
**Then** the system should save changes and generate an AuditLog entry

**Given** configuration changes are made
**When** the update is completed
**Then** changes should be reflected in real-time without system restart

**Given** configuration changes have been made
**When** I need to revert changes
**Then** rollback should be available for the last 10 configuration changes

### HU-03: Automatic AI Quota Management

**User Story**
As the system
I want to automatically control AI service usage
So that I can prevent cost overruns and ensure fair usage across tenants

**Acceptance Criteria (Gherkin Format)**
**Given** a tenant's AI usage reaches 90% of their quota
**When** the threshold is exceeded
**Then** a SystemAlert with level WARNING should be created

**Given** a tenant's AI usage reaches 100% of their quota
**When** they attempt to generate questions via /questions/generate
**Then** the system should return HTTP 429 Quota Exceeded

**Given** a tenant exceeds their AI quota threshold
**When** the limit is reached
**Then** automatic notifications should be sent to Admin Tenant and billing contact

**Given** a tenant has exceeded their quota
**When** they view their dashboard
**Then** an automatic upgrade option via Stripe should be available

---

## 2. Gestión de Usuarios y Autenticación

### HU-04: User Management and Email Verification

**User Story**
As a tenant administrator
I want to manage users in my organization with email verification
So that I can control access and maintain security standards

**Acceptance Criteria (Gherkin Format)**
**Given** I am creating a new user via POST /users
**When** the user creation is successful
**Then** an email with a verification token should be sent that expires in 24 hours

**Given** a user attempts to log in
**When** they provide valid credentials
**Then** the system should only allow login if Users.verified=TRUE

**Given** I need to invite multiple users
**When** I upload a CSV file with user data
**Then** the system should process up to 10,000 users in under 3 minutes using Celery

**Given** users have been inactive for more than 90 days
**When** the system runs its maintenance check
**Then** these users should be marked for review

### HU-05: Multi-Factor Authentication (MFA)

**User Story**
As a user
I want to enable multi-factor authentication on my account
So that I can increase the security of my data and access

**Acceptance Criteria (Gherkin Format)**
**Given** I want to enable MFA
**When** I set up TOTP authentication
**Then** the system should provide TOTP (RFC 6238) setup and 5 recovery codes

**Given** MFA is enforced for my role
**When** I log in without MFA configured
**Then** the system should redirect me to mandatory MFA setup

**Given** I have enabled MFA
**When** I complete the setup process
**Then** backup codes should be downloadable as a PDF

**Given** MFA is enabled on my account
**When** I attempt authentication
**Then** all authentication attempts should be logged for security audit

### HU-06: Enterprise Single Sign-On (SSO)

**User Story**
As a tenant administrator
I want to integrate with our identity provider
So that I can simplify employee access and maintain centralized authentication

**Acceptance Criteria (Gherkin Format)**
**Given** I want to configure SSO
**When** I set up the integration
**Then** the system should support SAML 2.0 & OIDC with metadata stored in TenantSSOConfigs

**Given** SSO is configured
**When** users authenticate via the identity provider
**Then** IdP groups should automatically map to internal Roles

**Given** I am configuring SSO
**When** I use the configuration wizard
**Then** I should be able to test the connection in under 30 seconds

**Given** the identity provider is unavailable
**When** users need to access the system
**Then** fallback to local login should be available only for SysAdmin

### HU-07: Temporary User Access

**User Story**
As a tenant administrator
I want to create users with time-limited access
So that I can provide temporary access for external evaluations or candidates

**Acceptance Criteria (Gherkin Format)**
**Given** I am creating a temporary user
**When** I send the invitation
**Then** the invitation should have configurable expiration (1 hour to 30 days)

**Given** a temporary user is created
**When** they access the system
**Then** they should only have access to specifically assigned exams

**Given** a temporary user's access expires
**When** the expiration time is reached
**Then** the user should be automatically deactivated

**Given** a temporary user has completed their evaluation
**When** their access expires
**Then** their results should remain exportable before final expiration

---

## 3. Gestión de Roles y Permisos (RBAC)

### HU-08: Create and Edit Custom Roles

**User Story**
As a tenant administrator
I want to create custom roles with specific permissions
So that I can delegate responsibilities in a granular way

**Acceptance Criteria (Gherkin Format)**
**Given** I am creating a new role
**When** I access the role creation interface
**Then** the UI should display 100% of available permissions from the Permissions table, categorized

**Given** I am creating a new role
**When** I submit the role via POST /roles
**Then** the system should create the role with version=1 and generate an audit log

**Given** I am configuring role permissions
**When** I use the permissions interface
**Then** it should provide a Discord-like interface with expandable categories

**Given** I am modifying role permissions
**When** I make changes
**Then** I should see a preview of changes before applying them

### HU-09: Clone and Version Roles

**User Story**
As a tenant administrator
I want to clone existing roles and maintain version history
So that I can facilitate role management and maintain traceability

**Acceptance Criteria (Gherkin Format)**
**Given** I want to clone an existing role
**When** I use the /roles/{id}/clone endpoint
**Then** a copy should be created with the suffix "(Copy)"

**Given** a role has been cloned
**When** the cloning process completes
**Then** the cloned permissions should be visible immediately

**Given** I am viewing role history
**When** I access the version history
**Then** I should see a visual diff between versions

**Given** I need to revert role changes
**When** I access the version history
**Then** rollback to a previous version should be available

### HU-10: Assign Roles to Users

**User Story**
As a tenant administrator
I want to assign and revoke user roles
So that I can control access based on responsibilities

**Acceptance Criteria (Gherkin Format)**
**Given** I assign or revoke a role
**When** the change is made
**Then** the RBAC cache should refresh in under 10 seconds

**Given** a role is assigned to a user
**When** the assignment is completed
**Then** a push notification should be sent to the recipient

**Given** I need to assign roles to multiple users
**When** I use the bulk assignment feature
**Then** I should be able to select multiple users for mass assignment

**Given** I am assigning conflicting permissions
**When** the system detects conflicts
**Then** permission conflict validation should prevent the assignment

### HU-11: Temporary Permissions

**User Story**
As a tenant administrator
I want to grant time-limited permissions
So that I can provide temporary access without permanent changes

**Acceptance Criteria (Gherkin Format)**
**Given** I am setting temporary permissions
**When** I configure the time limits
**Then** the system should validate that active_from and expires_at are at least 1 minute apart

**Given** temporary permissions have expired
**When** the hourly cron job runs
**Then** expired permissions should be automatically revoked

**Given** temporary permissions are about to expire
**When** 24 hours remain before expiration
**Then** a notification should be sent to the affected user

**Given** temporary permissions are expiring
**When** an extension is needed
**Then** automatic extension should be available with approval workflow

### HU-12: RBAC Change Auditing

**User Story**
As a tenant administrator
I want to view the history of permission changes
So that I can comply with audits and compliance requirements

**Acceptance Criteria (Gherkin Format)**
**Given** RBAC changes are made
**When** the changes are saved
**Then** JSON diffs should be stored in AuditLogs with before/after states

**Given** I need to export audit data
**When** I request an export as SysAdmin
**Then** the system should provide CSV export functionality

**Given** I am reviewing audit logs
**When** I access the audit interface
**Then** I should be able to filter by user, date, and change type

**Given** critical permission changes occur
**When** the changes are detected
**Then** automatic alerts should be generated for critical changes

---

## 4. Gestión de Cursos y Contenido

### HU-13: Course CRUD Operations

**User Story**
As a content creator
I want to create and manage courses
So that I can structure educational content effectively

**Acceptance Criteria (Gherkin Format)**
**Given** I am creating a new course
**When** I submit the course via POST /courses
**Then** the course should be created with status DRAFT

**Given** I want to remove a course
**When** I delete the course
**Then** the system should perform logical deletion via is_archived flag

**Given** I modify a published course
**When** I save the changes
**Then** the system should create automatic versioning for published changes

**Given** I am working on a course
**When** I want to review before publishing
**Then** a preview should be available before publication

### HU-14: Multimedia Content Management

**User Story**
As a content creator
I want to upload and manage multimedia files
So that I can enrich the educational content with various media types

**Acceptance Criteria (Gherkin Format)**
**Given** I am uploading a multimedia file
**When** the file exceeds 200MB or has invalid MIME type
**Then** the system should return HTTP 415 Unsupported Media Type

**Given** I upload a multimedia file successfully
**When** the upload completes
**Then** a SHA-256 checksum should be calculated and stored for integrity verification

**Given** I upload images or videos
**When** the files are processed
**Then** automatic compression should be applied to optimize file sizes

**Given** multimedia content is stored
**When** it needs to be accessed
**Then** CDN with presigned URLs should be used for secure and fast access

### HU-15: Course Cloning

**User Story**
As a content creator
I want to duplicate existing courses
So that I can reuse structure and content for similar courses

**Acceptance Criteria (Gherkin Format)**
**Given** I want to clone an existing course
**When** I use the /courses/{id}/clone endpoint
**Then** a duplicate should be created with "(Copy)" suffix

**Given** a course is cloned
**When** the cloning process completes
**Then** the tenant_id should be maintained and status reset to DRAFT

**Given** a course with multimedia content is cloned
**When** the cloning process runs
**Then** references to multimedia files should be copied appropriately

**Given** I am cloning a course
**When** I want to clone only the structure
**Then** I should have the option to clone structure without content

### HU-16: Prerequisites and Scheduling

**User Story**
As a content creator
I want to configure prerequisites and closing dates
So that I can control the learning flow and course availability

**Acceptance Criteria (Gherkin Format)**
**Given** I am setting a course closing date
**When** I configure the closing_at field
**Then** it should be greater than NOW() and auto-close should be scheduled

**Given** a student tries to access a course
**When** they haven't met the prerequisites
**Then** the system should return HTTP 409 Conflict

**Given** a course is approaching its closing date
**When** the notification time arrives
**Then** automatic notifications should be sent before closure

**Given** a course needs extended time
**When** an extension is requested
**Then** date extension should be available with proper justification

---

## 5. Banco de Preguntas e IA

### HU-17: Manual Question Creation

**User Story**
As a content creator
I want to create questions manually
So that I can have complete control over the content quality and structure

**Acceptance Criteria (Gherkin Format)**
**Given** I am creating a question manually
**When** I use the question editor
**Then** the system should support Markdown with real-time preview

**Given** I edit a published question
**When** I save the changes
**Then** the system should create a new version instead of overwriting

**Given** I am creating a question of a specific type
**When** I input the question data
**Then** the system should validate the structure according to the question type

**Given** I create question content
**When** the question is saved
**Then** automatic tags should be generated based on the content

### HU-18: AI-Powered Question Generation

**User Story**
As a content creator
I want to generate questions automatically using AI
So that I can accelerate content creation while maintaining quality

**Acceptance Criteria (Gherkin Format)**
**Given** I request AI question generation
**When** I provide prompts including CEFR level, skill, and tone
**Then** the system should generate 5 questions in under 6 seconds

**Given** AI question generation is completed
**When** the process finishes
**Then** the status should be set to READY and a WebSocket notification sent

**Given** AI questions are generated
**When** I review the results
**Then** I should be able to preview and edit questions before saving

**Given** AI generation is used
**When** questions are created
**Then** the system should track and log AI tokens consumed

### HU-19: Question Import Functionality

**User Story**
As a content creator
I want to import questions from external files
So that I can migrate existing content into the platform

**Acceptance Criteria (Gherkin Format)**
**Given** I import questions from a file
**When** the file format is CSV or QTI
**Then** the system should support both formats and indicate specific line/column for errors

**Given** I import a question file
**When** the import process completes
**Then** the original file should be saved for reference

**Given** I am importing questions
**When** the file contains errors
**Then** pre-validation should provide a detailed error report

**Given** I am importing a large question set
**When** the import process runs
**Then** a progress bar should show batch import progress

### HU-20: Question Versioning

**User Story**
As a content creator
I want to maintain versions of questions
So that I can track changes and rollback if needed

**Acceptance Criteria (Gherkin Format)**
**Given** a question is archived
**When** it's accessed from question pickers
**Then** is_archived should hide it from selection but preserve foreign key relationships

**Given** multiple versions of a question exist
**When** I want to edit the question
**Then** only the latest version should be editable

**Given** I am reviewing question history
**When** I compare versions
**Then** visual comparison between versions should be available

**Given** I want to analyze question performance
**When** I access question analytics
**Then** usage statistics should be available per version

### HU-21: Automatic Content Moderation

**User Story**
As the system
I want to automatically detect inappropriate content
So that I can maintain quality and compliance standards

**Acceptance Criteria (Gherkin Format)**
**Given** content is analyzed for appropriateness
**When** offensive content is detected
**Then** the content should be flagged with flagged=TRUE

**Given** content moderation flags are generated
**When** the daily digest runs
**Then** a summary should be sent to the Admin Tenant

**Given** critical inappropriate content is detected
**When** the severity threshold is exceeded
**Then** automatic escalation should occur for critical content

**Given** technical terms are used in content
**When** moderation analysis runs
**Then** domain-specific whitelists should prevent false positives

---

## 6. Exámenes y Evaluación

### HU-22: Exam Creation and Configuration

**User Story**
As a content creator
I want to create exams with flexible configuration options
So that I can evaluate specific competencies effectively

**Acceptance Criteria (Gherkin Format)**
**Given** I am creating a new exam
**When** the exam is created
**Then** it should start in DRAFT status with a maximum of 1 placement test per course

**Given** I am setting exam time limits
**When** I configure the time_limit_sec
**Then** it should not exceed 10,000 seconds (2.7 hours)

**Given** I am configuring exam settings
**When** I set up the exam parameters
**Then** I should be able to configure the number of allowed attempts

**Given** I want to vary question order
**When** I configure exam settings
**Then** question randomization should be available as an option

### HU-23: Question Selection for Exams

**User Story**
As a content creator
I want to select questions for exams
So that I can create balanced and comprehensive evaluations

**Acceptance Criteria (Gherkin Format)**
**Given** I am searching for questions
**When** I use the question search
**Then** full-text search should be available using pg_trgm

**Given** I am creating a skills-based exam
**When** I select questions
**Then** there should be a minimum of 1 question per skill being evaluated

**Given** I want balanced difficulty
**When** I select questions
**Then** automatic distribution by difficulty level should be available

**Given** I have selected questions for an exam
**When** I want to review the exam
**Then** a complete exam preview should be available

### HU-24: Exam Timer Configuration

**User Story**
As a content creator
I want to configure time limits for exams
So that I can standardize evaluations and ensure fair testing conditions

**Acceptance Criteria (Gherkin Format)**
**Given** I am configuring exam timing
**When** I set up the timer
**Then** I should be able to choose between global countdown or per-question timing

**Given** an exam timer expires
**When** the time limit is reached
**Then** the exam status should automatically change to TIMEOUT

**Given** a timed exam is in progress
**When** time is running out
**Then** warnings should be displayed at 10, 5, and 1 minute remaining

**Given** a student needs accessibility accommodations
**When** time extensions are required
**Then** time extension options should be available for accessibility compliance

### HU-25: Conditional Questions (Future Feature)

**User Story**
As a content creator
I want to configure branching logic for questions
So that I can create adaptive exams that adjust based on student responses

**Acceptance Criteria (Gherkin Format)**
**Given** the adaptive exam feature is being prepared
**When** the database schema is designed
**Then** a branch_logic JSONB column should be prepared for future use

**Given** the adaptive feature is not yet released
**When** users access the configuration UI
**Then** the branching configuration should be hidden until the feature flag is enabled

**Given** branching logic is configured
**When** the logic is validated
**Then** the system should detect and prevent circular logic

**Given** branching logic is set up
**When** I want to test the flow
**Then** flow simulation should be available before publishing

### HU-26: PDF Export for Exams

**User Story**
As a content creator
I want to generate printable versions of exams
So that I can provide backup options and offline distribution

**Acceptance Criteria (Gherkin Format)**
**Given** I want to export an exam
**When** I use the /export?format=pdf endpoint
**Then** a PDF should be generated with digital signature

**Given** a PDF is generated
**When** the export completes
**Then** the PDF should be retained in storage for 30 days

**Given** a PDF is generated for a tenant
**When** the PDF is created
**Then** it should include a watermark with tenant information

**Given** I am exporting an exam PDF
**When** I configure export options
**Then** I should be able to include or exclude correct answers

---

## 7. Presentación de Exámenes

### HU-27: Iniciar y reanudar intento
**Como** Estudiante  
**Quiero** presentar exámenes con posibilidad de reanudar  
**Para** completar evaluaciones sin perder progreso

**Criterios de Aceptación:**
- **CA1**: Único intento `IN_PROGRESS` por usuario
- **CA2**: Desconexión > 30min → status *ABANDONED*
- **CA3**: Reanudación desde última pregunta guardada
- **CA4**: Tracking de tiempo real transcurrido

### HU-28: Autosave de respuestas
**Como** Estudiante  
**Quiero** que mis respuestas se guarden automáticamente  
**Para** no perder trabajo por problemas técnicos

**Criterios de Aceptación:**
- **CA1**: Patch cada 10s; pérdida máxima ≤ 20 caracteres
- **CA2**: UI muestra "guardado" con status `204`
- **CA3**: Indicador visual de estado de guardado
- **CA4**: Recuperación automática tras reconexión

### HU-29: Reproducción de audio
**Como** Estudiante  
**Quiero** escuchar audio de preguntas listening  
**Para** completar evaluaciones auditivas

**Criterios de Aceptación:**
- **CA1**: HLS adaptativo, fallback MP3
- **CA2**: Botón repetir 1 vez si permitido por configuración
- **CA3**: Controles de volumen y velocidad
- **CA4**: Subtítulos opcionales para accesibilidad

### HU-30: Grabación de audio
**Como** Estudiante  
**Quiero** grabar respuestas de speaking  
**Para** demostrar competencias orales

**Criterios de Aceptación:**
- **CA1**: WebRTC Opus 48kHz; máximo 90 segundos
- **CA2**: Preview con opción de re-grabación única
- **CA3**: Indicador visual de nivel de audio
- **CA4**: Compresión automática para optimizar storage

### HU-31: Funcionalidad offline (PWA)
**Como** Estudiante  
**Quiero** continuar examen sin conexión  
**Para** completar evaluaciones en cualquier lugar

**Criterios de Aceptación:**
- **CA1**: IndexedDB ≤ 50MB, LRU purge automático
- **CA2**: Sync automático al recuperar conexión
- **CA3**: Banner de estado offline/online
- **CA4**: Validación de integridad post-sync

### HU-32: Accesibilidad
**Como** Usuario con discapacidad  
**Quiero** navegar con tecnologías asistivas  
**Para** acceso equitativo a evaluaciones

**Criterios de Aceptación:**
- **CA1**: Atajos de teclado (N/P para navegación)
- **CA2**: Contraste WCAG AA (4.5:1 mínimo)
- **CA3**: Screen reader compatible
- **CA4**: Tiempo extendido configurable

---

## 8. Corrección y Scoring

### HU-33: Auto-scoring MCQ
**Como** Sistema
**Quiero** calificar automáticamente preguntas de opción múltiple
**Para** proporcionar resultados inmediatos

**Criterios de Aceptación:**
- **CA1**: Respuesta correcta = `ai_score=1.0`
- **CA2**: Sumatoria simple para score total
- **CA3**: Procesamiento < 100ms por pregunta
- **CA4**: Logging de todas las calificaciones

### HU-34: Scoring con IA
**Como** Sistema
**Quiero** calificar respuestas abiertas con IA
**Para** evaluación objetiva y escalable

**Criterios de Aceptación:**
- **CA1**: GPT-4 con rúbrica 0-5 puntos < 4s
- **CA2**: `rubric_json` con campos `coherence`, `grammar`, `pronunciation`
- **CA3**: Confidence score para validación manual
- **CA4**: Fallback a evaluación manual si confidence < 70%

### HU-35: Detección de plagio
**Como** Sistema
**Quiero** detectar similitud en respuestas
**Para** mantener integridad académica

**Criterios de Aceptación:**
- **CA1**: Integración Turnitin; < 15% pasa automáticamente
- **CA2**: > 40% → `flagged=TRUE` para revisión manual
- **CA3**: Comparación interna entre respuestas del mismo examen
- **CA4**: Reporte detallado de similitudes encontradas

### HU-36: Override manual
**Como** Instructor
**Quiero** corregir calificaciones automáticas
**Para** ajustar evaluaciones según criterio experto

**Criterios de Aceptación:**
- **CA1**: Evaluador edita `ai_score`; guarda `updated_by`
- **CA2**: Notificación instantánea al candidato
- **CA3**: Justificación obligatoria para cambios > 1 punto
- **CA4**: Auditoría completa de overrides

---

## 9. Analítica y Reportes

### HU-37: Dashboards personalizables
**Como** Admin Tenant
**Quiero** visualizar métricas clave
**Para** tomar decisiones basadas en datos

**Criterios de Aceptación:**
- **CA1**: Filtros por equipo, fechas, cursos
- **CA2**: Carga < 2s para datasets de 10k registros
- **CA3**: Widgets drag-and-drop personalizables
- **CA4**: Exportación de gráficos en PNG/PDF

### HU-38: Exportación de datos
**Como** Admin Tenant
**Quiero** exportar datos en formatos estándar
**Para** análisis externo y compliance

**Criterios de Aceptación:**
- **CA1**: UTF-8, separador configurable para CSV
- **CA2**: Enlaces de descarga expiran en 24h
- **CA3**: Formatos: CSV, JSON, Excel, PDF
- **CA4**: Filtros aplicables antes de exportación

### HU-39: Webhooks para integraciones
**Como** Admin Tenant
**Quiero** recibir notificaciones automáticas
**Para** integrar con sistemas externos

**Criterios de Aceptación:**
- **CA1**: JSON API 1.0 + HMAC signature
- **CA2**: 5 reintentos con backoff exponencial
- **CA3**: Logs detallados de entregas exitosas/fallidas
- **CA4**: Test endpoint para validación

### HU-40: Analítica predictiva
**Como** Admin Tenant
**Quiero** predicciones sobre rendimiento
**Para** intervención temprana y mejora continua

**Criterios de Aceptación:**
- **CA1**: Modelo recalibra semanalmente
- **CA2**: AUC ≥ 75% en predicciones
- **CA3**: Alertas automáticas para estudiantes en riesgo
- **CA4**: Recomendaciones de contenido personalizado

---

## 10. Notificaciones y Comunicación

### HU-41: Notificaciones de finalización
**Como** Estudiante
**Quiero** recibir notificación cuando mis resultados estén listos
**Para** conocer mi progreso inmediatamente

**Criterios de Aceptación:**
- **CA1**: Email + push notification ≤ 2min tras corrección
- **CA2**: Link directo a feedback detallado
- **CA3**: Resumen de puntuación en la notificación
- **CA4**: Opción de mute por tipo de notificación

### HU-42: Alertas de cuota IA
**Como** Admin Tenant
**Quiero** recibir alertas de uso de IA
**Para** gestionar costos y planificar upgrades

**Criterios de Aceptación:**
- **CA1**: Alertas a 80/90/100% de cuota (INFO/WARN/CRITICAL)
- **CA2**: Canales: Slack + email configurables
- **CA3**: Proyección de uso basada en tendencia
- **CA4**: Link directo a upgrade de plan

### HU-43: Recordatorios de expiración
**Como** Sistema
**Quiero** recordar sobre exámenes próximos a expirar
**Para** maximizar tasa de completación

**Criterios de Aceptación:**
- **CA1**: Recordatorios a 24h y 1h antes de expiración
- **CA2**: No duplicar si examen ya terminado
- **CA3**: Personalización de horarios de envío
- **CA4**: Tracking de efectividad de recordatorios

---

## 11. Administración y Operaciones

### HU-44: Backups automáticos
**Como** SysAdmin
**Quiero** backups automáticos y confiables
**Para** garantizar continuidad del negocio

**Criterios de Aceptación:**
- **CA1**: Backup nocturno con gzip + checksum; retención 30 días
- **CA2**: `Backups.status` SUCCESS/FAIL con alertas
- **CA3**: Verificación de integridad automática
- **CA4**: Backup incremental diario, completo semanal

### HU-45: Restauración selectiva
**Como** SysAdmin
**Quiero** restaurar datos específicos
**Para** recuperación granular sin downtime completo

**Criterios de Aceptación:**
- **CA1**: `dry_run` muestra impacto antes de ejecutar
- **CA2**: Solo SysAdmin autorizado; log completo en audit
- **CA3**: Restauración por tenant sin afectar otros
- **CA4**: Rollback automático si restauración falla

### HU-46: Gestión de suscripciones
**Como** Admin Tenant
**Quiero** gestionar mi plan de suscripción
**Para** controlar costos y capacidades

**Criterios de Aceptación:**
- **CA1**: Stripe prorratea automáticamente upgrades/downgrades
- **CA2**: Actualiza `Tenants.plan` y muestra banner < 10s
- **CA3**: Downgrade con uso actual alto → `SystemAlert` grace 24h
- **CA4**: Historial completo de cambios de plan

### HU-47: Facturación automática
**Como** Sistema
**Quiero** procesar pagos automáticamente
**Para** operación sin intervención manual

**Criterios de Aceptación:**
- **CA1**: `invoice.paid` webhook → `TenantInvoices.PAID` + PDF email
- **CA2**: Tres reintentos de pago fallido; luego grace period 7 días
- **CA3**: PDF de factura retenido 7 años para compliance
- **CA4**: Alertas automáticas para pagos fallidos

### HU-48: Gestión de impagos
**Como** Sistema
**Quiero** manejar cuentas morosas automáticamente
**Para** proteger recursos y incentivar pagos

**Criterios de Aceptación:**
- **CA1**: Bloquea generación IA + nuevas invitaciones; intentos activos continúan
- **CA2**: Desbloqueo automático inmediato tras pago exitoso
- **CA3**: Notificaciones escaladas: 3, 7, 14 días de mora
- **CA4**: Exportación de datos disponible antes de suspensión

---

## 12. Integraciones Externas

### HU-49: Configuración SSO
**Como** Admin Tenant
**Quiero** configurar Single Sign-On
**Para** simplificar acceso de empleados

**Criterios de Aceptación:**
- **CA1**: Wizard valida `metadata.xml` + test login < 30s
- **CA2**: IdP caído → fallback a login local solo para SysAdmin
- **CA3**: Mapeo automático de grupos a roles
- **CA4**: Sincronización de usuarios en tiempo real

### HU-50: Webhooks HRIS/LMS
**Como** Admin Tenant
**Quiero** integrar con sistemas de RRHH
**Para** sincronización automática de datos

**Criterios de Aceptación:**
- **CA1**: Firmas HMAC con secreto por tenant
- **CA2**: Retry automático y logs en `WebhookLogs`
- **CA3**: Rate limiting configurable por endpoint
- **CA4**: Transformación de datos configurable

### HU-51: API pública
**Como** Desarrollador externo
**Quiero** acceder a funcionalidades vía API
**Para** crear integraciones personalizadas

**Criterios de Aceptación:**
- **CA1**: OAuth2 con scopes específicos por funcionalidad
- **CA2**: Rate limiting 1000 RPM; `429` si excede
- **CA3**: Documentación OpenAPI completa con ejemplos
- **CA4**: SDK en Python, JavaScript, PHP

---

## 13. Funcionalidades Faltantes Críticas

### HU-52: Course Enrollment Management

**User Story**
As a tenant administrator
I want to manage student enrollment in courses
So that I can control access and track participation

**Acceptance Criteria (Gherkin Format)**
**Given** I want to enroll students in a course
**When** I select students and assign them to a course
**Then** the system should create enrollment records with status ACTIVE

**Given** a student is enrolled in a course
**When** they access the platform
**Then** they should see the course in their dashboard

**Given** I need to unenroll a student
**When** I remove their enrollment
**Then** their access should be revoked but progress data preserved

**Given** enrollment limits are set for a course
**When** the limit is reached
**Then** new enrollment attempts should be blocked with appropriate messaging

### HU-53: Student Progress Tracking

**User Story**
As an instructor
I want to track student progress through courses
So that I can identify students who need additional support

**Acceptance Criteria (Gherkin Format)**
**Given** a student is progressing through a course
**When** they complete activities
**Then** their progress percentage should be updated in real-time

**Given** I am viewing student progress
**When** I access the progress dashboard
**Then** I should see completion rates, time spent, and performance metrics

**Given** a student is falling behind
**When** their progress drops below threshold
**Then** automatic alerts should be generated for intervention

**Given** I want to export progress data
**When** I request a progress report
**Then** detailed CSV/PDF reports should be available

### HU-54: Course Completion and Certificates

**User Story**
As a student
I want to receive certificates upon course completion
So that I can demonstrate my achievements

**Acceptance Criteria (Gherkin Format)**
**Given** I complete all required course activities
**When** the completion criteria are met
**Then** a digital certificate should be automatically generated

**Given** a certificate is generated
**When** I access my profile
**Then** I should be able to download the certificate as PDF

**Given** certificates are issued
**When** external verification is needed
**Then** a public verification URL should be available

**Given** certificate templates are configured
**When** certificates are generated
**Then** they should include tenant branding and digital signatures

### HU-55: Exam Invitation Management

**User Story**
As a content creator
I want to send exam invitations to external candidates
So that I can evaluate people who are not regular platform users

**Acceptance Criteria (Gherkin Format)**
**Given** I want to invite external candidates
**When** I create an exam invitation
**Then** unique access links should be generated with expiration dates

**Given** an invitation is sent
**When** the candidate clicks the link
**Then** they should have direct access to the exam without full registration

**Given** invitations have expiration dates
**When** the deadline passes
**Then** access links should become invalid automatically

**Given** I want to track invitation status
**When** I view the invitation dashboard
**Then** I should see sent, opened, started, and completed statistics

### HU-56: Question Pool Management

**User Story**
As a content creator
I want to organize questions into pools by topic and difficulty
So that I can efficiently create balanced exams

**Acceptance Criteria (Gherkin Format)**
**Given** I am organizing questions
**When** I create question pools
**Then** I should be able to categorize by topic, difficulty, and skill type

**Given** I am creating an exam
**When** I select from question pools
**Then** I should be able to auto-select questions based on distribution criteria

**Given** question pools are defined
**When** I want to maintain quality
**Then** I should be able to review and approve questions before they enter the pool

**Given** I want to analyze pool effectiveness
**When** I access pool analytics
**Then** I should see usage statistics and performance metrics per pool

### HU-57: Exam Proctoring Features

**User Story**
As a content creator
I want basic proctoring capabilities for high-stakes exams
So that I can maintain exam integrity

**Acceptance Criteria (Gherkin Format)**
**Given** proctoring is enabled for an exam
**When** a student takes the exam
**Then** tab switching and window focus changes should be detected and logged

**Given** suspicious behavior is detected
**When** proctoring flags are raised
**Then** the exam should be marked for manual review

**Given** proctoring data is collected
**When** the exam is completed
**Then** a proctoring report should be available to instructors

**Given** camera access is available
**When** photo proctoring is enabled
**Then** periodic photos should be captured and stored securely

### HU-58: Bulk Operations Management

**User Story**
As a tenant administrator
I want to perform bulk operations on users and content
So that I can efficiently manage large datasets

**Acceptance Criteria (Gherkin Format)**
**Given** I need to update multiple users
**When** I select users for bulk operations
**Then** I should be able to change roles, status, or group assignments simultaneously

**Given** I want to bulk import content
**When** I upload structured data files
**Then** the system should process and validate all entries with detailed error reporting

**Given** bulk operations are running
**When** I monitor the progress
**Then** real-time progress indicators and completion estimates should be available

**Given** bulk operations complete
**When** I review the results
**Then** detailed success/failure reports should be provided with rollback options

### HU-59: Mobile App Synchronization

**User Story**
As a student using mobile devices
I want seamless synchronization between web and mobile
So that I can continue my learning across devices

**Acceptance Criteria (Gherkin Format)**
**Given** I start an exam on mobile
**When** I switch to web browser
**Then** my progress should be synchronized automatically

**Given** I am offline on mobile
**When** I complete activities
**Then** data should sync when connection is restored

**Given** I have the mobile app installed
**When** I receive notifications
**Then** they should be consistent across web and mobile platforms

**Given** I download content for offline use
**When** content is updated online
**Then** I should receive notifications to sync the latest version

### HU-60: Advanced Search and Filtering

**User Story**
As a content creator
I want advanced search capabilities across all content
So that I can quickly find and reuse existing materials

**Acceptance Criteria (Gherkin Format)**
**Given** I am searching for content
**When** I use the advanced search
**Then** I should be able to filter by type, difficulty, tags, creation date, and author

**Given** I want to find similar questions
**When** I use semantic search
**Then** the system should return questions with similar meaning or topics

**Given** I am searching across large datasets
**When** I perform searches
**Then** results should be returned in under 2 seconds with pagination

**Given** I want to save search criteria
**When** I create a search filter
**Then** I should be able to save and reuse complex search queries

---

## Resumen de Secciones Restantes

**Nota**: Las siguientes secciones (HU-27 a HU-51) mantienen la misma estructura de formato Gherkin pero se resumen aquí por brevedad. Cada historia incluye:

- **User Story**: Descripción clara del actor, acción y beneficio
- **Acceptance Criteria (Gherkin Format)**: Criterios Given/When/Then verificables

### Secciones Incluidas:

**7. Presentación de Exámenes (HU-27 a HU-32)**
- Iniciar y reanudar intentos de examen
- Autosave automático de respuestas
- Reproducción de audio para preguntas listening
- Grabación de audio para preguntas speaking
- Funcionalidad offline (PWA)
- Características de accesibilidad

**8. Corrección y Scoring (HU-33 a HU-36)**
- Auto-scoring de preguntas MCQ
- Scoring con IA para respuestas abiertas
- Detección de plagio
- Override manual de calificaciones

**9. Analítica y Reportes (HU-37 a HU-40)**
- Dashboards personalizables
- Exportación de datos en múltiples formatos
- Webhooks para integraciones
- Analítica predictiva

**10. Notificaciones y Comunicación (HU-41 a HU-43)**
- Notificaciones de finalización de exámenes
- Alertas de cuota IA
- Recordatorios de expiración

**11. Administración y Operaciones (HU-44 a HU-48)**
- Backups automáticos
- Restauración selectiva
- Gestión de suscripciones
- Facturación automática
- Gestión de impagos

**12. Integraciones Externas (HU-49 a HU-51)**
- Configuración SSO empresarial
- Webhooks HRIS/LMS
- API pública con OAuth2

**13. Funcionalidades Críticas Adicionales (HU-52 a HU-60)**
- Gestión de inscripciones a cursos
- Seguimiento de progreso estudiantil
- Certificados de finalización
- Invitaciones a exámenes externos
- Gestión de pools de preguntas
- Características básicas de proctoring
- Operaciones masivas
- Sincronización móvil
- Búsqueda avanzada y filtrado

---

## Conclusión

Estos **60 requisitos funcionales** proporcionan la base completa para el desarrollo de Arroyo University, cubriendo todos los aspectos desde la gestión multi-tenant hasta funcionalidades avanzadas de aprendizaje. Cada historia de usuario sigue el formato Gherkin con criterios de aceptación verificables que aseguran:

- **Trazabilidad**: Cada requisito es rastreable desde la concepción hasta la implementación
- **Testabilidad**: Los criterios Given/When/Then permiten automatización de pruebas
- **Claridad**: La estructura uniforme facilita la comprensión por todos los stakeholders
- **Completitud**: Cobertura integral de funcionalidades core y avanzadas

La estructura modular permite desarrollo incremental mientras mantiene coherencia en la experiencia de usuario y cumplimiento de estándares internacionales de calidad y seguridad.
